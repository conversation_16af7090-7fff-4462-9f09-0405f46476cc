"""
结直肠癌筛查微观模拟模型

这是一个用于结直肠癌筛查策略评估的微观模拟模型，支持：
- 双通路疾病进展模型（腺瘤-癌变通路和锯齿状腺瘤通路）
- 多种筛查工具和策略
- 卫生经济学评估
- 机器学习参数校准

主要模块：
- core: 核心模拟引擎
- modules: 疾病、筛查、经济学模块
- calibration: 机器学习校准
- interfaces: 用户界面
"""

__version__ = "1.0.0"
__author__ = "结直肠癌筛查模型团队"

# 导入核心类
from .core.engine import MicrosimulationEngine
from .core.individual import Individual
from .core.population import Population

# 导入疾病模型
from .modules.disease.disease_model import DiseaseModel
from .modules.disease.enums import DiseaseState, CancerStage, PathwayType

# 导入筛查模型
from .modules.screening.screening_model import ScreeningModel

# 导入经济模型
from .modules.economics.economic_model import EconomicModel

__all__ = [
    "MicrosimulationEngine",
    "Individual", 
    "Population",
    "DiseaseModel",
    "DiseaseState",
    "CancerStage", 
    "PathwayType",
    "ScreeningModel",
    "EconomicModel"
]
