# Story 1.4: 生命表集成和死亡率建模

## Status
Draft

## Story
**As a** 模拟引擎，
**I want** 基于中国生命表应用自然死亡率，
**so that** 准确建模人群老龄化和自然死亡。

## Acceptance Criteria
1. 实现生命表数据加载和验证系统
2. 创建年龄和性别特异性死亡率计算函数
3. 实现随机抽样的年度死亡率应用
4. 添加人群生存统计跟踪
5. 创建生命表数据格式文档
6. 实现死亡率计算单元测试

## Tasks / Subtasks

- [ ] 任务1：实现生命表数据管理系统 (AC: 1)
  - [ ] 创建src/modules/population/life_table.py文件
  - [ ] 实现LifeTable类，加载和管理生命表数据
  - [ ] 添加CSV格式生命表数据解析功能
  - [ ] 实现数据验证（年龄范围、死亡率范围检查）
  - [ ] 添加多个生命表支持（中国、WHO全球等）
  - [ ] 实现生命表数据缓存和索引优化

- [ ] 任务2：创建死亡率计算引擎 (AC: 2)
  - [ ] 在LifeTable类中实现get_mortality_rate方法
  - [ ] 添加年龄和性别特异性死亡率查询
  - [ ] 实现死亡率插值计算（处理非整数年龄以及非连贯年龄）
  - [ ] 添加死亡率平滑处理功能
  - [ ] 实现死亡率趋势调整（年份校正）
  - [ ] 添加死亡率不确定性建模支持

- [ ] 任务3：实现年度死亡率应用机制 (AC: 3)
  - [ ] 创建src/core/mortality_engine.py文件
  - [ ] 实现MortalityEngine类，管理死亡率应用
  - [ ] 添加随机抽样死亡判定功能
  - [ ] 实现批量死亡率计算和应用
  - [ ] 添加死亡原因分类（自然死亡vs癌症死亡）
  - [ ] 实现死亡时间精确计算（月份级别）

- [ ] 任务4：添加人群生存统计跟踪 (AC: 4)
  - [ ] 扩展Population类，添加生存统计功能
  - [ ] 实现生存曲线计算和跟踪
  - [ ] 添加年龄特异性生存率统计
  - [ ] 实现队列生存分析功能
  - [ ] 添加生存时间分布计算
  - [ ] 创建生存统计可视化准备功能

- [ ] 任务5：创建生命表数据和文档 (AC: 5)
  - [ ] 创建data/life_tables/目录结构
  - [ ] 添加中国2020年生命表数据（china_2020.csv）
  - [ ] 添加WHO全球生命表数据（who_global.csv）
  - [ ] 创建生命表数据格式规范文档
  - [ ] 编写生命表使用指南和API文档
  - [ ] 添加数据来源和更新说明

- [ ] 任务6：实现死亡率建模测试套件 (AC: 6)
  - [ ] 创建tests/unit/test_life_table.py测试文件
  - [ ] 创建tests/unit/test_mortality_engine.py测试文件
  - [ ] 实现生命表数据加载和验证测试
  - [ ] 添加死亡率计算准确性测试
  - [ ] 创建随机抽样死亡判定测试
  - [ ] 实现生存统计计算验证测试

## Dev Notes

### 生命表数据格式
```xlsx
# data/life_tables/china_2020.xlsx
age,gender,mortality_rate,life_expectancy,survival_probability
0,male,0.00654,75.1,0.99346
0,female,0.00521,80.9,0.99479
1,male,0.00043,74.2,0.99957
1,female,0.00035,79.9,0.99965
...
50,male,0.00312,26.8,0.99688
50,female,0.00198,31.2,0.99802
...
```

### LifeTable类核心方法
- `load_life_table(file_path)`: 加载生命表数据
- `get_mortality_rate(age, gender)`: 获取特定年龄性别死亡率
- `get_survival_probability(age, gender)`: 获取生存概率
- `interpolate_rate(age, gender)`: 非整数年龄及非插值
- `validate_data()`: 数据完整性验证
- `get_life_expectancy(age, gender)`: 获取预期寿命

### MortalityEngine类核心功能
- **死亡判定**: 基于概率的随机死亡判定
- **批量处理**: 高效处理大规模人群死亡率
- **时间精度**: 支持月份级别的死亡时间计算
- **死亡分类**: 区分自然死亡和疾病相关死亡
- **统计跟踪**: 记录死亡率应用统计信息

### 死亡率计算逻辑
```python
def apply_mortality(individual, current_time):
    """应用年度死亡率"""
    age = individual.get_age_at_time(current_time)
    mortality_rate = life_table.get_mortality_rate(age, individual.gender)
    
    # 转换为月度死亡概率
    monthly_rate = 1 - (1 - mortality_rate) ** (1/12)
    
    # 随机判定是否死亡
    if random.random() < monthly_rate:
        individual.transition_to_state(DiseaseState.DEATH_OTHER, current_time)
        return True
    return False
```

### 生存统计功能
- **生存曲线**: Kaplan-Meier生存估计
- **生存率**: 年龄特异性生存率计算
- **中位生存时间**: 队列中位生存时间
- **生存分析**: 按性别、年龄组的生存分析
- **风险评估**: 死亡风险评估和预测

### 数据验证规则
- 死亡率范围: 0 ≤ mortality_rate ≤ 1
- 年龄范围: 0 ≤ age ≤ 120
- 性别值: "male", "female"
- 生存概率: survival_probability = 1 - mortality_rate
- 数据完整性: 所有年龄性别组合都有数据

### 性能优化
- 生命表数据预加载和索引
- 批量死亡率计算减少查询次数
- 使用NumPy向量化操作提高计算效率
- 实现死亡率查询缓存机制

### Testing
#### 测试文件位置
- `tests/unit/test_life_table.py`
- `tests/unit/test_mortality_engine.py`
- `tests/integration/test_mortality_application.py`

#### 测试标准
- 生命表数据加载正确性验证
- 死亡率计算精度测试（与标准值比较）
- 随机抽样统计检验（大数定律验证）
- 生存统计计算准确性测试
- 边界条件和异常处理测试

#### 测试框架和模式
- 使用pytest fixtures提供测试生命表数据
- 统计检验验证随机抽样准确性
- Mock随机数生成器测试确定性行为
- 性能测试验证大规模死亡率计算

#### 特定测试要求
- 死亡率精度: 计算结果与标准值误差 < 0.1%
- 统计准确性: 大样本随机抽样偏差 < 5%
- 性能要求: 10万个体死亡率计算 < 5秒
- 数据完整性: 所有年龄性别组合都能正确查询

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-07-31 | 1.0 | 初始故事创建 | Scrum Master |

## Dev Agent Record
*此部分将由开发代理在实施过程中填写*

### Agent Model Used
*待填写*

### Debug Log References
*待填写*

### Completion Notes List
*待填写*

### File List
*待填写*

## QA Results
*此部分将由QA代理在完成故事实施的QA审查后填写*
